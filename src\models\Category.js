const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true,
    maxlength: 50
  },
  description: { 
    type: String, 
    maxlength: 200 
  },
  color: { 
    type: String, 
    default: '#3498db',
    match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  },
  icon: { 
    type: String,
    default: 'folder'
  },
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Index để tăng tốc query
categorySchema.index({ userId: 1, name: 1 }, { unique: true });
categorySchema.index({ userId: 1, createdAt: -1 });

// Middleware để update updatedAt
categorySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual để đếm số task trong category
categorySchema.virtual('taskCount', {
  ref: 'Task',
  localField: '_id',
  foreignField: 'categoryId',
  count: true
});

module.exports = mongoose.model('Category', categorySchema);