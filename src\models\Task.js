const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  title: { 
    type: String, 
    required: true,
    trim: true,
    maxlength: 100
  },
  description: { 
    type: String,
    maxlength: 500
  },
  categoryId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Category', 
    required: true 
  },
  startDate: { 
    type: Date, 
    default: Date.now 
  },
  deadline: { 
    type: Date, 
    required: true 
  },
  isCompleted: { 
    type: Boolean, 
    default: false 
  },
  completedAt: { 
    type: Date 
  },
  priority: { 
    type: String, 
    enum: ['low', 'medium', 'high'], 
    default: 'medium' 
  },
  imagePath: { 
    type: String 
  },
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Index để tăng tốc query
taskSchema.index({ userId: 1, categoryId: 1 });
taskSchema.index({ userId: 1, isCompleted: 1 });
taskSchema.index({ userId: 1, deadline: 1 });
taskSchema.index({ userId: 1, createdAt: -1 });

// Middleware để update updatedAt
taskSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Tự động set completedAt khi task được hoàn thành
  if (this.isCompleted && !this.completedAt) {
    this.completedAt = Date.now();
  }
  
  // Xóa completedAt khi task chưa hoàn thành
  if (!this.isCompleted && this.completedAt) {
    this.completedAt = undefined;
  }
  
  next();
});

// Virtual để check task có quá hạn không
taskSchema.virtual('isOverdue').get(function() {
  return !this.isCompleted && this.deadline < new Date();
});

module.exports = mongoose.model('Task', taskSchema);