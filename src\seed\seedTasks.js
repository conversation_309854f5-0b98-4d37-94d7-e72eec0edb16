const mongoose = require('mongoose');
const Task = require('../models/Task');
const Category = require('../models/Category');
const User = require('../models/User');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const seedTasks = async () => {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for seeding tasks');

    // Lấy danh sách users và categories
    const users = await User.find({});
    const categories = await Category.find({});
    
    if (users.length === 0) {
      console.log('❌ No users found. Please run seed:users first');
      process.exit(1);
    }
    
    if (categories.length === 0) {
      console.log('❌ No categories found. Please run seed:categories first');
      process.exit(1);
    }

    // <PERSON><PERSON>a dữ liệu tasks cũ
    await Task.deleteMany({});
    console.log('Cleared existing tasks');

    // Dữ liệu tasks mẫu theo category
    const taskTemplatesByCategory = {
      'Công việc': [
        {
          title: 'Hoàn thành báo cáo tháng',
          description: 'Viết báo cáo tổng kết công việc tháng này và gửi cho quản lý',
          priority: 'high',
          daysFromNow: 7
        },
        {
          title: 'Chuẩn bị presentation',
          description: 'Làm slide presentation cho cuộc họp tuần tới',
          priority: 'medium',
          daysFromNow: 5
        },
        {
          title: 'Review code của team',
          description: 'Kiểm tra và review code của các thành viên trong team',
          priority: 'medium',
          daysFromNow: 3
        }
      ],
      'Học tập': [
        {
          title: 'Học Node.js',
          description: 'Hoàn thành khóa học Node.js trên Udemy',
          priority: 'medium',
          daysFromNow: 14
        },
        {
          title: 'Đọc sách "Clean Code"',
          description: 'Đọc và ghi chú những điểm quan trọng trong sách Clean Code',
          priority: 'low',
          daysFromNow: 21
        },
        {
          title: 'Học tiếng Anh',
          description: 'Học 30 từ vựng mới và luyện speaking',
          priority: 'medium',
          daysFromNow: 1
        }
      ],
      'Sức khỏe': [
        {
          title: 'Tập thể dục',
          description: 'Tập gym 3 lần trong tuần',
          priority: 'high',
          daysFromNow: 3
        },
        {
          title: 'Khám sức khỏe định kỳ',
          description: 'Đặt lịch và đi khám sức khỏe tổng quát',
          priority: 'medium',
          daysFromNow: 30
        }
      ],
      'Gia đình': [
        {
          title: 'Mua quà sinh nhật mẹ',
          description: 'Chọn và mua quà sinh nhật cho mẹ',
          priority: 'high',
          daysFromNow: 10
        },
        {
          title: 'Gọi điện cho bà ngoại',
          description: 'Gọi điện thăm hỏi sức khỏe bà ngoại',
          priority: 'medium',
          daysFromNow: 2
        }
      ],
            'Cá nhân': [
        {
          title: 'Dọn dẹp phòng ngủ',
          description: 'Sắp xếp lại đồ đạc và dọn dẹp phòng ngủ',
          priority: 'low',
          daysFromNow: 2
        },
        {
          title: 'Cập nhật CV',
          description: 'Cập nhật thông tin và kinh nghiệm mới vào CV',
          priority: 'medium',
          daysFromNow: 15
        }
      ],
      'Mua sắm': [
        {
          title: 'Mua thực phẩm tuần này',
          description: 'Đi siêu thị mua thực phẩm cho cả tuần',
          priority: 'high',
          daysFromNow: 1
        },
        {
          title: 'Mua quần áo mùa đông',
          description: 'Mua áo khoác và quần áo ấm cho mùa đông',
          priority: 'medium',
          daysFromNow: 20
        }
      ],
      'Giải trí': [
        {
          title: 'Xem phim mới',
          description: 'Xem bộ phim mới được đề xuất trên Netflix',
          priority: 'low',
          daysFromNow: 3
        },
        {
          title: 'Chơi game với bạn bè',
          description: 'Tổ chức buổi chơi game online với nhóm bạn',
          priority: 'low',
          daysFromNow: 5
        }
      ],
      'Du lịch': [
        {
          title: 'Lên kế hoạch du lịch Đà Lạt',
          description: 'Tìm hiểu địa điểm, đặt khách sạn và lập lịch trình',
          priority: 'medium',
          daysFromNow: 45
        },
        {
          title: 'Đặt vé máy bay',
          description: 'Đặt vé máy bay cho chuyến du lịch cuối năm',
          priority: 'high',
          daysFromNow: 60
        }
      ]
    };

    // Tạo tasks cho mỗi user
    const tasks = [];
    
    for (const user of users) {
      const userCategories = categories.filter(cat => cat.userId.toString() === user._id.toString());
      
      for (const category of userCategories) {
        const categoryTasks = taskTemplatesByCategory[category.name] || [];
        
        // Tạo 2-4 tasks cho mỗi category
        const numTasks = Math.min(categoryTasks.length, Math.floor(Math.random() * 3) + 2);
        
        for (let i = 0; i < numTasks; i++) {
          const template = categoryTasks[i];
          if (!template) continue;
          
          const startDate = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
          const deadline = new Date(Date.now() + template.daysFromNow * 24 * 60 * 60 * 1000);
          
          // Random 30% tasks đã hoàn thành
          const isCompleted = Math.random() < 0.3;
          
          const task = {
            title: `${template.title} - ${user.username}`,
            description: template.description,
            categoryId: category._id,
            startDate: startDate,
            deadline: deadline,
            priority: template.priority,
            isCompleted: isCompleted,
            completedAt: isCompleted ? new Date(startDate.getTime() + Math.random() * (Date.now() - startDate.getTime())) : undefined,
            userId: user._id,
            createdAt: startDate
          };
          
          tasks.push(task);
        }
      }
    }

    // Thêm tasks vào database
    const createdTasks = await Task.insertMany(tasks);
    console.log(`✅ Created ${createdTasks.length} tasks successfully`);

    // In thống kê
    console.log('\n📊 Task Statistics:');
    users.forEach(user => {
      const userTasks = createdTasks.filter(task => task.userId.toString() === user._id.toString());
      const completedTasks = userTasks.filter(task => task.isCompleted);
      const pendingTasks = userTasks.filter(task => !task.isCompleted);
      
      console.log(`- ${user.username}: ${userTasks.length} tasks (${completedTasks.length} completed, ${pendingTasks.length} pending)`);
    });

    // In tasks theo category
    console.log('\n📋 Tasks by Category:');
    const categoryNames = [...new Set(categories.map(cat => cat.name))];
    categoryNames.forEach(categoryName => {
      const categoryTasks = createdTasks.filter(task => {
        const taskCategory = categories.find(cat => cat._id.toString() === task.categoryId.toString());
        return taskCategory && taskCategory.name === categoryName;
      });
      console.log(`- ${categoryName}: ${categoryTasks.length} tasks`);
    });

    mongoose.connection.close();
    console.log('\nDatabase connection closed');
  } catch (error) {
    console.error('Error seeding tasks:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedTasks();
}

module.exports = seedTasks;
          