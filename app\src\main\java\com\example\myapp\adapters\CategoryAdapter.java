package com.example.myapp.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.myapp.R;
import com.example.myapp.models.Category;

import java.util.List;

public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder> {

    private List<Category> categoryList;
    private OnCategoryClickListener listener;
    private int selectedPosition = 0; // Default to "Tất cả"

    public interface OnCategoryClickListener {
        void onCategoryClick(Category category, int position);
    }

    public CategoryAdapter(List<Category> categoryList, OnCategoryClickListener listener) {
        this.categoryList = categoryList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_category, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        Category category = categoryList.get(position);
        holder.bind(category, position);
    }

    @Override
    public int getItemCount() {
        return categoryList.size();
    }

    public void setSelectedPosition(int position) {
        int previousPosition = selectedPosition;
        selectedPosition = position;
        notifyItemChanged(previousPosition);
        notifyItemChanged(selectedPosition);
    }

    public class CategoryViewHolder extends RecyclerView.ViewHolder {
        private View categoryColorIndicator;
        private TextView textCategoryName;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            categoryColorIndicator = itemView.findViewById(R.id.categoryColorIndicator);
            textCategoryName = itemView.findViewById(R.id.textCategoryName);
        }

        public void bind(Category category, int position) {
            textCategoryName.setText(category.getName());

            // Set color indicator
            try {
                int color = Color.parseColor(category.getColor());
                categoryColorIndicator.setBackgroundColor(color);
            } catch (IllegalArgumentException e) {
                // Default color if parsing fails
                categoryColorIndicator.setBackgroundColor(Color.parseColor("#3498db"));
            }

            // Highlight selected category
            if (position == selectedPosition) {
                itemView.setBackgroundColor(Color.parseColor("#E3F2FD"));
                textCategoryName.setTextColor(Color.parseColor("#1976D2"));
            } else {
                itemView.setBackgroundColor(Color.parseColor("#FFFFFF"));
                textCategoryName.setTextColor(Color.parseColor("#333333"));
            }

            // Set click listener
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    setSelectedPosition(getAdapterPosition());
                    listener.onCategoryClick(category, getAdapterPosition());
                }
            });
        }
    }
}
