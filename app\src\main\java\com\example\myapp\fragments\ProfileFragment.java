package com.example.myapp.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.example.myapp.R;

public class ProfileFragment extends Fragment {

    private TextView textUserName, textUserEmail, textCompletedTasks, textPendingTasks;
    private ImageButton buttonMenu, buttonSettings;
    private LinearLayout navTasks, navCalendar, navProfile;
    private LinearLayout menuEditProfile, menuNotifications, menuPrivacy, menuHelp, menuLogout;

    private OnNavigationClickListener navigationListener;

    public interface OnNavigationClickListener {
        void onTasksClick();
        void onCalendarClick();
        void onProfileClick();
        void onMenuClick();
    }

    public ProfileFragment() {
        // Required empty public constructor
    }

    public static ProfileFragment newInstance() {
        return new ProfileFragment();
    }

    public void setOnNavigationClickListener(OnNavigationClickListener listener) {
        this.navigationListener = listener;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_profile, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupClickListeners();
        loadUserData();
    }

    private void initViews(View view) {
        textUserName = view.findViewById(R.id.textUserName);
        textUserEmail = view.findViewById(R.id.textUserEmail);
        textCompletedTasks = view.findViewById(R.id.textCompletedTasks);
        textPendingTasks = view.findViewById(R.id.textPendingTasks);
        buttonMenu = view.findViewById(R.id.buttonMenu);
        buttonSettings = view.findViewById(R.id.buttonSettings);
        navTasks = view.findViewById(R.id.navTasks);
        navCalendar = view.findViewById(R.id.navCalendar);
        navProfile = view.findViewById(R.id.navProfile);

        // Menu items
        menuEditProfile = view.findViewById(R.id.menuEditProfile);
        menuNotifications = view.findViewById(R.id.menuNotifications);
        menuPrivacy = view.findViewById(R.id.menuPrivacy);
        menuHelp = view.findViewById(R.id.menuHelp);
        menuLogout = view.findViewById(R.id.menuLogout);
    }

    private void setupClickListeners() {
        navTasks.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onTasksClick();
            }
        });

        navCalendar.setOnClickListener(v -> {
            if (navigationListener != null) {
                navigationListener.onCalendarClick();
            }
        });

        navProfile.setOnClickListener(v -> {
            // Already on profile screen
        });

        // Menu item click listeners
        menuEditProfile.setOnClickListener(v -> {
            // TODO: Open edit profile screen
        });

        menuNotifications.setOnClickListener(v -> {
            // TODO: Open notifications settings
        });

        menuPrivacy.setOnClickListener(v -> {
            // TODO: Open privacy settings
        });

        menuHelp.setOnClickListener(v -> {
            // TODO: Open help screen
        });

        menuLogout.setOnClickListener(v -> {
            // TODO: Handle logout
        });

        buttonSettings.setOnClickListener(v -> {
            // TODO: Open settings
        });
    }

    private void loadUserData() {
        // Load sample user data
        textUserName.setText("Người dùng");
        textUserEmail.setText("<EMAIL>");
        textCompletedTasks.setText("12");
        textPendingTasks.setText("5");
    }
}
