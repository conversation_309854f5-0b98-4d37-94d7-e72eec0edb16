{"name": "todo-list-app", "version": "1.0.0", "description": "A Todo List application with Node.js, Express, and MongoDB", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "seed:users": "node src/seed/seedUser.js", "seed:categories": "node src/seed/seedCategories.js", "seed:tasks": "node src/seed/seedTasks.js", "seed:all": "node src/seed/seedAll.js", "seed:clean": "node -e \"require('mongoose').connect(process.env.MONGO_URI).then(() => Promise.all([require('./src/models/User').deleteMany({}), require('./src/models/Category').deleteMany({}), require('./src/models/Task').deleteMany({})])).then(() => console.log('Database cleaned')).finally(() => process.exit())\""}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.1.7"}}