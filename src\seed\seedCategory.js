const mongoose = require('mongoose');
const Category = require('../models/Category');
const User = require('../models/User');
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const seedCategories = async () => {
  try {
    // Kết nối MongoDB
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected for seeding categories');

    // Lấy danh sách users
    const users = await User.find({});
    if (users.length === 0) {
      console.log('❌ No users found. Please run seed:users first');
      process.exit(1);
    }

    // Xóa dữ liệu categories cũ
    await Category.deleteMany({});
    console.log('Cleared existing categories');

    // Dữ liệu categories mẫu
    const categoryTemplates = [
      {
        name: '<PERSON><PERSON><PERSON> việc',
        description: '<PERSON><PERSON><PERSON> công việc liên quan đến nghề nghiệp',
        color: '#e74c3c',
        icon: 'briefcase'
      },
      {
        name: '<PERSON><PERSON><PERSON> tập',
        description: 'Các hoạt động học tập và phát triển bản thân',
        color: '#3498db',
        icon: 'book'
      },
      {
        name: 'Cá nhân',
        description: 'Các công việc cá nhân hàng ngày',
        color: '#9b59b6',
        icon: 'user'
      },
      {
        name: 'Gia đình',
        description: 'Các hoạt động liên quan đến gia đình',
        color: '#e67e22',
        icon: 'home'
      },
      {
        name: 'Sức khỏe',
        description: 'Các hoạt động chăm sóc sức khỏe',
        color: '#27ae60',
        icon: 'heart'
      },
      {
        name: 'Mua sắm',
        description: 'Danh sách mua sắm và chi tiêu',
        color: '#f39c12',
        icon: 'shopping-cart'
      },
      {
        name: 'Giải trí',
        description: 'Các hoạt động giải trí và thư giãn',
        color: '#1abc9c',
        icon: 'gamepad'
      },
      {
        name: 'Du lịch',
        description: 'Kế hoạch du lịch và khám phá',
        color: '#34495e',
        icon: 'plane'
      }
    ];

    // Tạo categories cho mỗi user
    const categories = [];
    users.forEach(user => {
      categoryTemplates.forEach(template => {
        categories.push({
          ...template,
          userId: user._id
        });
      });
    });

    // Thêm categories vào database
    const createdCategories = await Category.insertMany(categories);
    console.log(`✅ Created ${createdCategories.length} categories successfully`);

    // In thống kê
    console.log('\n📊 Category Statistics:');
    users.forEach(user => {
      const userCategories = createdCategories.filter(cat => cat.userId.toString() === user._id.toString());
      console.log(`- ${user.username}: ${userCategories.length} categories`);
    });

    // In categories theo user đầu tiên
    const firstUser = users[0];
    const firstUserCategories = createdCategories.filter(cat => cat.userId.toString() === firstUser._id.toString());
    console.log(`\n📋 Categories for ${firstUser.username}:`);
    firstUserCategories.forEach(cat => {
      console.log(`- ${cat.name} (${cat.color}) - ${cat.description}`);
    });

    mongoose.connection.close();
    console.log('\nDatabase connection closed');
  } catch (error) {
    console.error('Error seeding categories:', error);
    process.exit(1);
  }
};

// Chạy seed nếu file được gọi trực tiếp
if (require.main === module) {
  seedCategories();
}

module.exports = seedCategories;