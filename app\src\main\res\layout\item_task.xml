<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with checkbox and priority -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <CheckBox
                android:id="@+id/checkboxCompleted"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textTaskTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Task title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/textTaskDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Task description"
                    android:textSize="14sp"
                    android:textColor="#666666"
                    android:maxLines="2"
                    android:ellipsize="end"
                    android:visibility="gone" />

            </LinearLayout>

            <!-- Priority indicator -->
            <View
                android:id="@+id/priorityIndicator"
                android:layout_width="8dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="#FF9800" />

            <ImageButton
                android:id="@+id/buttonTaskMenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_menu_more"
                android:contentDescription="More options"
                android:padding="8dp" />

        </LinearLayout>

        <!-- Footer with deadline and category -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Category -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@android:drawable/btn_default"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp">

                <View
                    android:id="@+id/categoryColorDot"
                    android:layout_width="8dp"
                    android:layout_height="8dp"
                    android:background="@android:color/holo_blue_light"
                    android:layout_marginEnd="6dp" />

                <TextView
                    android:id="@+id/textCategoryName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Category"
                    android:textSize="12sp"
                    android:textColor="#666666" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Deadline -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@android:drawable/ic_menu_recent_history"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/textDeadline"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="25/12/2024"
                    android:textSize="12sp"
                    android:textColor="#666666" />

            </LinearLayout>

        </LinearLayout>

        <!-- Overdue indicator -->
        <TextView
            android:id="@+id/textOverdue"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="⚠️ Quá hạn"
            android:textSize="12sp"
            android:textColor="#F44336"
            android:textStyle="bold"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
