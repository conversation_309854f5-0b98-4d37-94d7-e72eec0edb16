<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="#2196F3"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:elevation="4dp">

        <ImageButton
            android:id="@+id/buttonMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_sort_by_size"

            android:contentDescription="Menu"
            android:padding="12dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="Cá nhân"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@android:color/white" />

        <ImageButton
            android:id="@+id/buttonSettings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_preferences"

            android:contentDescription="Settings"
            android:padding="12dp" />

    </LinearLayout>

    <!-- Profile Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Profile Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:background="#E3F2FD"
                android:padding="32dp">

                <ImageView
                    android:id="@+id/imageProfile"
                    android:layout_width="100dp"
                    android:layout_height="100dp"
                    android:src="@android:drawable/ic_menu_myplaces"
                    android:background="@android:drawable/btn_default"
                    android:backgroundTint="@android:color/white"
                    android:padding="20dp"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/textUserName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Người dùng"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/black"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/textUserEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="16sp"
                    android:textColor="#666666" />

            </LinearLayout>

            <!-- Statistics -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:background="@android:drawable/btn_default"
                    android:backgroundTint="#E8F5E8"
                    android:padding="16dp"
                    android:layout_marginEnd="8dp">

                    <TextView
                        android:id="@+id/textCompletedTasks"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="12"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="#4CAF50" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Đã hoàn thành"
                        android:textSize="12sp"
                        android:textColor="#666666"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:background="@android:drawable/btn_default"
                    android:backgroundTint="#FFF3E0"
                    android:padding="16dp"
                    android:layout_marginStart="8dp">

                    <TextView
                        android:id="@+id/textPendingTasks"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="5"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="#FF9800" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Đang thực hiện"
                        android:textSize="12sp"
                        android:textColor="#666666"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- Menu Options -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Edit Profile -->
                <LinearLayout
                    android:id="@+id/menuEditProfile"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_edit"
                         />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Chỉnh sửa thông tin"
                        android:textSize="16sp"
                        android:textColor="@android:color/black" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@android:drawable/ic_menu_more"
                         />

                </LinearLayout>

                <!-- Notifications -->
                <LinearLayout
                    android:id="@+id/menuNotifications"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_popup_reminder"
                       />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Thông báo"
                        android:textSize="16sp"
                        android:textColor="@android:color/black" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@android:drawable/ic_menu_more"
                         />

                </LinearLayout>

                <!-- Data & Privacy -->
                <LinearLayout
                    android:id="@+id/menuPrivacy"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_info_details"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Dữ liệu  Quyền riêng tư"
                        android:textSize="16sp"
                        android:textColor="@android:color/black" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@android:drawable/ic_menu_more"
                         />

                </LinearLayout>

                <!-- Help & Support -->
                <LinearLayout
                    android:id="@+id/menuHelp"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_help"
                       />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Trợ giúp  Hỗ trợ"
                        android:textSize="16sp"
                        android:textColor="@android:color/black" />

                    <ImageView
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@android:drawable/ic_menu_more"
                        />

                </LinearLayout>

                <!-- Logout -->
                <LinearLayout
                    android:id="@+id/menuLogout"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:background="?attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:layout_marginTop="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_revert"
                         />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:text="Đăng xuất"
                        android:textSize="16sp"
                        android:textColor="#F44336" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- Footer Navigation -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:background="@android:color/white"
        android:elevation="8dp">

        <LinearLayout
            android:id="@+id/navTasks"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_agenda"
                 />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Nhiệm vụ"
                android:textSize="10sp"
                android:textColor="#666666"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/navCalendar"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_today"
                 />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Lịch"
                android:textSize="10sp"
                android:textColor="#666666"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/navProfile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:background="?attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_myplaces"
                />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cá nhân"
                android:textSize="10sp"
                android:textColor="#2196F3"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
