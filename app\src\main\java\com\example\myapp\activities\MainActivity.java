package com.example.myapp.activities;

import android.os.Bundle;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.example.myapp.R;
import com.example.myapp.fragments.TodoFragment;
import com.example.myapp.fragments.CalendarFragment;
import com.example.myapp.fragments.ProfileFragment;

public class MainActivity extends AppCompatActivity implements TodoFragment.OnNavigationListener, CalendarFragment.OnNavigationClickListener, ProfileFragment.OnNavigationClickListener {

    private static final String TAG_TODO = "TodoFragment";
    private static final String TAG_CALENDAR = "CalendarFragment";
    private static final String TAG_PROFILE = "ProfileFragment";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Add TodoFragment to the activity
        if (savedInstanceState == null) {
            showTodoFragment();
        }
    }

    private void showTodoFragment() {
        TodoFragment todoFragment = TodoFragment.newInstance();
        todoFragment.setOnNavigationListener(this);
        replaceFragment(todoFragment, TAG_TODO);
    }

    private void showCalendarFragment() {
        CalendarFragment calendarFragment = CalendarFragment.newInstance();
        calendarFragment.setOnNavigationClickListener(this);
        replaceFragment(calendarFragment, TAG_CALENDAR);
    }

    private void showProfileFragment() {
        ProfileFragment profileFragment = ProfileFragment.newInstance();
        profileFragment.setOnNavigationClickListener(this);
        replaceFragment(profileFragment, TAG_PROFILE);
    }

    private void replaceFragment(Fragment fragment, String tag) {
        FragmentManager fragmentManager = getSupportFragmentManager();
        FragmentTransaction fragmentTransaction = fragmentManager.beginTransaction();
        fragmentTransaction.replace(R.id.fragment_container, fragment, tag);
        fragmentTransaction.commit();
    }

    // TodoFragment.OnNavigationListener implementation
    @Override
    public void onNavigateToCalendar() {
        showCalendarFragment();
    }

    @Override
    public void onNavigateToProfile() {
        showProfileFragment();
    }

    // CalendarFragment.OnNavigationClickListener implementation
    @Override
    public void onTasksClick() {
        showTodoFragment();
    }

    @Override
    public void onCalendarClick() {
        // Already on calendar screen
    }

    @Override
    public void onMenuClick() {
        // Handle menu click if needed
    }

    // ProfileFragment.OnNavigationClickListener implementation
    @Override
    public void onProfileClick() {
        showProfileFragment();
    }
}
