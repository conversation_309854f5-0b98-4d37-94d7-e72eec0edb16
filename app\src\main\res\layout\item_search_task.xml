<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_margin="4dp" app:cardCornerRadius="8dp" app:cardElevation="2dp">
    <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="horizontal" android:padding="12dp" android:gravity="center_vertical">
        <View android:id="@+id/priorityIndicator" android:layout_width="4dp" android:layout_height="40dp" android:background="#FF9800" android:layout_marginEnd="12dp"/>
        <LinearLayout android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:orientation="vertical">
            <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content" android:orientation="horizontal" android:gravity="center_vertical">
                <TextView android:id="@+id/textTaskTitle" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" android:text="Task title" android:textSize="16sp" android:textStyle="bold" android:textColor="@android:color/black" android:maxLines="1" android:ellipsize="end"/>
                <TextView android:id="@+id/textTaskType" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="Nhiệm vụ" android:textSize="10sp" android:textColor="@android:color/white" android:background="#2196F3" android:paddingStart="6dp" android:paddingEnd="6dp" android:paddingTop="2dp" android:paddingBottom="2dp" android:layout_marginStart="8dp"/>
            </LinearLayout>
            <TextView android:id="@+id/textTaskDescription" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginTop="4dp" android:text="Task description" android:textSize="14sp" android:textColor="#666666" android:maxLines="2" android:ellipsize="end"/>
            <TextView android:id="@+id/textTaskDeadline" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginTop="4dp" android:text="Hạn: 25/12/2024" android:textSize="12sp" android:textColor="#999999"/>
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>